from typing import Any, Dict, List
import json
import logging
import urllib.parse
import httpx
from constants import (
    PDFPLUS_AUTH_KEY,
    PDFPLUS_URL,
    PDFPLUS_EXTRACT_FIELDS_ENDPOINT,
)
from utils.fields_mapping import (
    get_missing_fields_mapping,
    map_missing_fields_to_prompts,
    map_api_response_to_columns,
)


async def call_pdfplus_extract_fields_api(
    refer_id: str, ocr_data: str, missing_fields: List[str]
) -> Dict[str, Any]:
    """
    Extract missing fields from OCR data using the PDFPlus API.
    Args:
        refer_id: The refer_id of the referral.
        ocr_data: The OCR data of the referral.
        missing_fields: The list of missing fields to extract.
    Returns:
        Dict[str, Any]: The extracted missing fields.
    """
    logging.info(
        f"Calling PDFPlus API for refer_id={refer_id}, missing_fields={missing_fields}"
    )

    prompts_mapping = await get_missing_fields_mapping()
    prompts = await map_missing_fields_to_prompts(missing_fields, prompts_mapping)
    response = await send_pdfplus_request(refer_id, ocr_data, prompts)

    if not response:
        return {}

    return await map_api_response_to_columns(response, prompts, prompts_mapping)


async def send_pdfplus_request(
    refer_id: str, ocr_data: str, prompts: List[str]
) -> Dict[str, Any]:
    """
    Send a request to the PDFPlus API to extract missing fields from OCR data.
    Args:
        refer_id: The refer_id of the referral.
        ocr_data: The OCR data of the referral.
        prompts: The list of prompts to extract.
    Returns:
        Dict[str, Any]: The response from the API.
    """
    files = {"ocr_data": ("ocr_data.json", json.dumps(ocr_data), "application/json")}
    form_data = {"data_points": json.dumps({"questions": prompts})}

    async with httpx.AsyncClient(timeout=httpx.Timeout(60)) as client:
        try:
            response = await client.post(
                url=urllib.parse.urljoin(
                    PDFPLUS_URL.rstrip("/"), PDFPLUS_EXTRACT_FIELDS_ENDPOINT.lstrip("/")
                ),
                data=form_data,
                files=files,
                headers={"Authorization": f"Basic {PDFPLUS_AUTH_KEY}"},
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logging.error(f"API call failed for refer_id={refer_id}: {e.response.text}")
            return {}
        except httpx.ReadTimeout as e:
            logging.error(
                f"API call failed for refer_id={refer_id} due to timeout: {e}"
            )
            return {}
        except Exception as e:
            logging.error(f"API call failed for refer_id={refer_id}: {e}")
            return {}
