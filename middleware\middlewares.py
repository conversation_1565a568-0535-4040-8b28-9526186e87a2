from fastapi import Request
import time
import logging
from typing import Callable
from fastapi import Response
import uuid
from utils.logging_config import REQUEST_ID_VAR


async def http_middleware(request: Request, call_next: Callable) -> Response:
    """
    The HTTP middleware function.
    Args:
        request (Request): The request object.
        call_next (Callable): The next middleware function.
    Returns:
        Response: The response object.
    """
    REQUEST_ID_VAR.set(str(uuid.uuid4()))

    start_time = time.time()

    client_host = request.client.host
    url_path = request.url.path
    logging.info(f"started: {client_host} | {request.method} | {url_path}")

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
