from typing import Dict, List, Tuple, Optional, Any
import json
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, not_, bindparam
from sqlalchemy.dialects.postgresql import insert
from datetime import datetime, timezone
from models.monitoring import Monitoring, InterfaceLog, AgentRuns
from helpers.json_parse_helper import DateTimeEncoder
from exceptions import CustomError
from fastapi import status
from sqlalchemy.exc import SQLAlchemyError
from utils.db_utils import get_model


async def update_monitoring_details(
    db: AsyncSession,
    alerted_to: str,
    errors: Dict,
    alerted: bool = False,
    updated_at: datetime = datetime.now(timezone.utc),
) -> None:
    """
    Update monitoring details in the database using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        alerted_to: Email address to send alerts to.
        errors: Dictionary of errors to store.
        alerted: Whether an alert was sent.
        updated_at: Timestamp of the update.
    """
    try:
        errors_json = json.dumps(errors, cls=DateTimeEncoder)
        errors_dict = json.loads(errors_json)

        if alerted:
            logging.info(
                f"Updating monitoring and alert details! | AlertedTo: {alerted_to} | UpdatedAt: {updated_at}"
            )
            stmt = (
                insert(Monitoring)
                .values(
                    id=1,
                    alerted_to=alerted_to,
                    errors=errors_dict,
                    last_run_at=updated_at,
                    last_alerted_at=updated_at,
                )
                .on_conflict_do_update(
                    index_elements=["id"],
                    set_={
                        "alerted_to": alerted_to,
                        "errors": errors_dict,
                        "last_run_at": updated_at,
                        "last_alerted_at": updated_at,
                    },
                )
            )
        else:
            logging.info(f"Updating monitoring details! | UpdatedAt: {updated_at}")
            stmt = (
                insert(Monitoring)
                .values(id=1, errors=errors_dict, last_run_at=updated_at)
                .on_conflict_do_update(
                    index_elements=["id"],
                    set_={"errors": errors_dict, "last_run_at": updated_at},
                )
            )

        await db.execute(stmt)
        await db.commit()
        logging.info("Updated monitoring details!")
    except SQLAlchemyError as e:
        await db.rollback()
        logging.error(f"Database error while updating monitoring details: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        logging.error(f"Error updating monitoring details: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)


async def get_monitoring_details(db: AsyncSession) -> Tuple[Dict, datetime]:
    """
    Get monitoring details from the database using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.

    Returns:
        Tuple[Dict, datetime]: Tuple containing errors dictionary and last run timestamp.
    """
    try:
        stmt = select(Monitoring).where(Monitoring.id == 1)
        monitoring_record = await db.execute(stmt)
        monitoring_record = monitoring_record.scalar_one_or_none()
        if monitoring_record:
            return monitoring_record.errors, monitoring_record.last_run_at
        return {}, None
    except SQLAlchemyError as e:
        logging.error(f"Database error while getting monitoring details: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(f"Error getting monitoring details: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)


async def get_errors(db: AsyncSession, last_run_time: datetime) -> List[Tuple]:
    """
    Get errors from the interface log table using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        last_run_time: Timestamp to filter errors by.

    Returns:
        List[Tuple]: List of error tuples.
    """
    try:
        stmt = (
            select(
                InterfaceLog.interface_name,
                InterfaceLog.event_id,
                InterfaceLog.client_id,
                InterfaceLog.error_type,
                InterfaceLog.error_details,
                func.min(InterfaceLog.end_time).label("error_start_time"),
                func.max(InterfaceLog.end_time).label("error_last_time"),
                func.count(InterfaceLog.end_time).label("error_count"),
            )
            .where(
                and_(
                    InterfaceLog.success.is_(False),
                    not_(
                        or_(
                            InterfaceLog.error_type.is_(None),
                            func.length(func.trim(InterfaceLog.error_type)) == 0,
                        )
                    ),
                    InterfaceLog.end_time > bindparam("last_run_time"),
                )
            )
            .group_by(
                InterfaceLog.interface_name,
                InterfaceLog.event_id,
                InterfaceLog.client_id,
                InterfaceLog.error_type,
                InterfaceLog.error_details,
            )
        )

        errors = await db.execute(stmt, {"last_run_time": last_run_time})
        errors = errors.fetchall()
        return errors
    except SQLAlchemyError as e:
        logging.error(f"Database error while getting errors: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(f"Error getting errors: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)


async def update_agent_run_timestamp(
    db: AsyncSession,
    agent_name: str,
    schema_name: str,
    run_timestamp: Optional[datetime] = None,
) -> Dict[str, Any]:
    """
    Update the last run timestamp for an agent in the database.

    Args:
        db: SQLAlchemy async database session.
        agent_name: Name of the agent.
        schema_name: Schema name for the database.
        run_timestamp: Timestamp of the run (defaults to current time).

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        if run_timestamp is None:
            run_timestamp = datetime.now(timezone.utc)

        AgentRunsModel = await get_model(AgentRuns, schema_name)

        stmt = (
            insert(AgentRunsModel)
            .values(agent_name=agent_name, last_run_at=run_timestamp)
            .on_conflict_do_update(
                index_elements=["agent_name"],
                set_={"last_run_at": run_timestamp},
            )
        )

        await db.execute(stmt)
        await db.commit()
        logging.info(f"Updated {agent_name} run timestamp to {run_timestamp}.")

        return {
            "status": "SUCCESS",
            "message": f"Agent run timestamp updated successfully for {agent_name}.",
            "agent_name": agent_name,
            "last_run_at": run_timestamp,
        }
    except SQLAlchemyError as e:
        await db.rollback()
        logging.error(f"Database error while updating agent run timestamp: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        logging.error(f"Error updating agent run timestamp: {e}")
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def get_agent_last_run(
    db: AsyncSession, agent_name: str, schema_name: str
) -> Optional[datetime]:
    """
    Get the last run timestamp for an agent from the database.

    Args:
        db: SQLAlchemy async database session.
        agent_name: Name of the agent.
        schema_name: Schema name for the database.

    Returns:
        Optional[datetime]: Last run timestamp or None if not found.
    """
    try:
        AgentRunsModel = await get_model(AgentRuns, schema_name)

        stmt = select(AgentRunsModel).where(AgentRunsModel.agent_name == agent_name)
        agent_last_run_record = await db.execute(stmt)
        agent_last_run_record = agent_last_run_record.scalar_one_or_none()

        if agent_last_run_record:
            return agent_last_run_record.last_run_at
        return None
    except SQLAlchemyError as e:
        logging.error(f"Database error while getting agent last run: {e}")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.error(f"Error getting agent last run: {e}")
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )
