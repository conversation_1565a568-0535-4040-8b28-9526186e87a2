pipeline {
    agent any
    environment {
        S3_BUCKET = 'lincware-jenkins'
        BRANCH = 'main'
    }
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        stage('Read Version') {
            steps {
                script {
                    version = readFile('version.txt').trim()
                    echo "Building version: ${version}"
                }
            }
        }
        stage('Build Docker Image') {
            steps {
                sh "docker build --no-cache --pull -t referral-ai:${version} ."
            }
        }
        stage('Save Docker Image') {
            steps {
                sh "docker save referral-ai:${version} -o referral-ai-${version}.tar"
            }
        }
        stage('Upload to S3') {
            steps {
                sh "aws s3 cp referral-ai-${version}.tar s3://${S3_BUCKET}/referral-ai/${BRANCH}/${BUILD_NUMBER}/referral-ai-${version}.tar"
            }
        }
        stage('Build cleanup') {
            steps {
                script {
                    try {
                        sh '''
                            docker images | grep -E "referral-ai|python" | awk '{print $3}' | xargs -r docker rmi -f
                            rm -f referral-ai-*.tar
                            docker builder prune -f
                        '''
                    } catch (Exception e) {
                        echo "Warning: Cleanup encountered an issue: ${e.getMessage()}"
                        currentBuild.result = 'SUCCESS'
                    }
                }
            }
        }
    }
    post {
        always {
            echo 'Pipeline completed!'
        }
    }
    triggers {
        pollSCM('H/5 * * * *')
    }
}
