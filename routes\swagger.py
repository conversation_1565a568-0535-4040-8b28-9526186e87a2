from fastapi import APIRouter, Request
from fastapi import Depends
from fastapi.security import HTTPBasicCredentials
from auth import authenticate_swagger_calls
from ratelimit import limiter
from fastapi.openapi.docs import get_swagger_ui_html
from helpers.version_helper import get_version
from constants import ROOT_PATH


swagger_router = APIRouter()


@swagger_router.get("/docs", include_in_schema=False)
@limiter.limit("5/minute")
async def custom_swagger_ui_html(
    request: Request,
    credentials: HTTPBasicCredentials = Depends(authenticate_swagger_calls),
):
    return get_swagger_ui_html(
        openapi_url=f"{ROOT_PATH}/openapi.json", title=f"Referral AI {get_version()}"
    )


@swagger_router.get("/openapi.json", include_in_schema=False)
@limiter.limit("5/minute")
async def custom_openapi(
    request: Request,
    credentials: HTTPBasicCredentials = Depends(authenticate_swagger_calls),
):
    openapi_schema = request.app.openapi()
    openapi_schema["servers"] = [
        {"url": ROOT_PATH},
    ]
    return openapi_schema
