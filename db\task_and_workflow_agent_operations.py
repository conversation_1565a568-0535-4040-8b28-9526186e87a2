from fastapi import status
from constants import (
    CREATED_BY_USER_ID,
    CREATED_BY_USERNAME,
    COMMENT,
    COMMENT_TYPE,
    TASK_SUBJECT,
    STATUS,
    TASK_CREATION_WAITING_MINUTES,
    HIGH_PRIORITY_FIELDS,
)
from utils.db_utils import get_model
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, select, update
from sqlalchemy.exc import SQLAlchemyError
from exceptions import CustomError
from typing import List, Dict, Any, Callable, TypeVar, Awaitable
from datetime import datetime, timedelta, timezone
from models import PatientReferralAI, ReferralComment, ReferralTask

T = TypeVar("T")


async def get_referrals_with_pending_tasks(
    schema_name: str, db: AsyncSession
) -> List[Dict[str, Any]]:
    """
    Get all rows from the gs_patient_referral_ai table where `require_task` is True
    and the `created_at` timestamp is older than the threshold time using SQLAlchemy.

    Args:
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: List of rows that meet the criteria.
    """
    try:
        threshold_time = datetime.now(timezone.utc) - timedelta(
            minutes=TASK_CREATION_WAITING_MINUTES
        )

        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        query = select(
            PatientReferralAIModel.refer_id,
            PatientReferralAIModel.patient_id,
            PatientReferralAIModel.res_fields_json,
            PatientReferralAIModel.created_at,
            PatientReferralAIModel.require_task,
            PatientReferralAIModel.task_created,
        ).where(
            and_(
                PatientReferralAIModel.require_task == True,
                PatientReferralAIModel.task_created == False,
                PatientReferralAIModel.created_at < threshold_time,
            )
        )

        pending_tasks_records = await db.execute(query)
        rows = pending_tasks_records.fetchall()
        columns = pending_tasks_records.keys()
        referrals_with_pending_tasks = [dict(zip(columns, row)) for row in rows]
        logging.info(
            f"Retrieved {len(referrals_with_pending_tasks)} referrals with pending tasks."
        )
        return referrals_with_pending_tasks

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def execute_in_transaction(
    operation: Callable[[AsyncSession, Any], Awaitable[T]],
    *args,
    db: AsyncSession,
    **kwargs,
) -> T:
    """
    Executes a series of database operations within a single transaction using SQLAlchemy.

    Args:
        operation: A callable that performs the database operations.
        *args: Positional arguments to pass to the operation.
        db: SQLAlchemy async database session.
        **kwargs: Keyword arguments to pass to the operation.

    Returns:
        Any: The result of the operation.
    """
    try:
        async with db.begin():
            task_creation_result = await operation(db, *args, **kwargs)
            return task_creation_result
    except SQLAlchemyError as e:
        logging.info("Reverted the task creation transaction.")
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logging.info("Reverted the task creation transaction.")
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def create_tasks_for_missing_fields_in_transaction(
    task_list: List[Dict[str, Any]],
    client_schema: str,
    db: AsyncSession,
) -> List[Dict[str, Any]]:
    """
    Creates tasks for missing fields in a single transaction using SQLAlchemy.

    Args:
        task_list: List of tasks to process.
        client_schema: The schema name for the database.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: Summary of created tasks.
    """

    async def operation(db: AsyncSession):
        logging.info(
            f"Starting task creation transaction. Tasks to create: {len(task_list)}"
        )
        agent_summary = []

        for task in task_list:
            refer_id = task["refer_id"]
            patient_id = task["patient_id"]
            res_fields_json = task["res_fields_json"]

            missing_data_points = []
            for key, value in res_fields_json.items():
                if value is None or value == "":
                    missing_data_points.append(key)

            if not missing_data_points:
                logging.info(
                    f"No missing data points for refer_id={refer_id}. Skipping."
                )
                continue

            priority = (
                "PD"
                if not set(missing_data_points).isdisjoint(set(HIGH_PRIORITY_FIELDS))
                else "N"
            )

            referral_comment = await add_referral_comment_in_db(
                db=db,
                patient_id=patient_id,
                refer_id=refer_id,
                priority=priority,
                data_point=",".join(missing_data_points),
                schema_name=client_schema,
            )
            referral_comment_id = referral_comment["comment_id"]

            task_response = await create_task_for_missing_info(
                db=db,
                cmt_id=referral_comment_id,
                patient_id=patient_id,
                refer_id=refer_id,
                priority=priority,
                schema_name=client_schema,
                data_point=",".join(missing_data_points),
            )
            agent_summary.append(task_response)

            await update_created_task_status(
                db=db, refer_id=refer_id, schema_name=client_schema
            )

        logging.info("Task creation transaction completed.")
        return agent_summary

    return await execute_in_transaction(operation, db=db)


async def add_referral_comment_in_db(
    db: AsyncSession,
    patient_id: str,
    refer_id: str,
    priority: str,
    data_point: str,
    schema_name: str,
) -> Dict[str, Any]:
    """
    Add a referral comment to the database using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        patient_id: Patient ID.
        refer_id: Referral ID.
        priority: Priority level.
        data_point: Data point description.
        schema_name: Schema name.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    ReferralCommentModel = await get_model(ReferralComment, schema_name)

    current_time = datetime.now(timezone.utc)
    new_comment = ReferralCommentModel(
        patient_id=patient_id,
        refer_id=refer_id,
        created_date=current_time,
        created_by=CREATED_BY_USER_ID,
        created=CREATED_BY_USERNAME,
        cmt_type=COMMENT_TYPE,
        subject=TASK_SUBJECT,
        status=STATUS,
        priority=priority,
        cmt=COMMENT.format(data_point=data_point),
    )

    db.add(new_comment)
    await db.flush()

    comment_id = new_comment.id
    logging.info(f"Comment created successfully for refer_id: {refer_id}.")

    return {
        "status": "SUCCESS",
        "message": f"Comment created successfully.",
        "comment_id": comment_id,
        "refer_id": refer_id,
    }


async def create_task_for_missing_info(
    db: AsyncSession,
    cmt_id: int,
    patient_id: str,
    refer_id: str,
    priority: str,
    schema_name: str,
    data_point: str,
) -> Dict[str, Any]:
    """
    Create a task for missing information using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        cmt_id: Comment ID.
        patient_id: Patient ID.
        refer_id: Referral ID.
        priority: Priority level.
        schema_name: Schema name.
        data_point: Data point description.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    ReferralTaskModel = await get_model(ReferralTask, schema_name)

    current_time = datetime.now(timezone.utc)
    new_task = ReferralTaskModel(
        cmt_id=cmt_id,
        patient_id=patient_id,
        refer_id=refer_id,
        task_type=COMMENT_TYPE,
        subject=TASK_SUBJECT,
        status=STATUS,
        priority=priority,
        created_date=current_time,
        created_by=CREATED_BY_USER_ID,
        created=CREATED_BY_USERNAME,
        created_task=COMMENT.format(data_point=data_point),
        modified_date=current_time,
        modified_by=CREATED_BY_USER_ID,
        modified=CREATED_BY_USERNAME,
        modified_task=COMMENT.format(data_point=data_point),
    )

    db.add(new_task)
    await db.flush()

    task_id = new_task.id
    logging.info(f"Task created successfully for refer_id: {refer_id}.")

    return {
        "status": "SUCCESS",
        "message": f"Task created successfully.",
        "task_id": task_id,
        "refer_id": refer_id,
    }


async def update_created_task_status(
    db: AsyncSession, refer_id: int, schema_name: str
) -> Dict[str, Any]:
    """
    Update the task created status for a referral using SQLAlchemy.

    Args:
        db: SQLAlchemy async database session.
        refer_id: Referral ID.
        schema_name: Schema name.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

    stmt = select(PatientReferralAIModel).where(
        PatientReferralAIModel.refer_id == refer_id
    )
    patient_referral_ai_record = await db.execute(stmt)
    patient_referral_ai_record = patient_referral_ai_record.scalar_one_or_none()

    if not patient_referral_ai_record:
        return {
            "status": "ERROR",
            "message": f"Referral AI record not found for refer_id: {refer_id}",
            "refer_id": refer_id,
        }

    patient_referral_ai_record.task_created = True
    logging.info(f"Task status updated successfully for refer_id: {refer_id}.")

    return {
        "status": "SUCCESS",
        "message": f"Task created status updated for refer_id: {refer_id}",
        "refer_id": refer_id,
    }


async def get_referrals_with_auto_complete_tasks(
    schema_name: str, db: AsyncSession
) -> List[Dict[str, Any]]:
    """
    Get all the tasks that need to be auto-completed.

    Args:
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: List of rows that meet the criteria.
    """
    try:
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)
        ReferralTaskModel = await get_model(ReferralTask, schema_name)

        query = (
            select(
                PatientReferralAIModel.refer_id,
                PatientReferralAIModel.patient_id,
            )
            .join(
                ReferralTaskModel,
                PatientReferralAIModel.refer_id == ReferralTaskModel.refer_id,
            )
            .where(
                and_(
                    PatientReferralAIModel.require_task == False,
                    PatientReferralAIModel.task_created == True,
                    ReferralTaskModel.status != "Complete",
                    ReferralTaskModel.subject == TASK_SUBJECT,
                )
            )
        )

        patient_referral_ai_record = await db.execute(query)
        rows = patient_referral_ai_record.fetchall()
        columns = patient_referral_ai_record.keys()

        referrals_with_tasks_to_auto_complete = [
            dict(zip(columns, row)) for row in rows
        ]
        logging.info(
            f"Retrieved {len(referrals_with_tasks_to_auto_complete)} referrals with tasks to auto-complete."
        )
        return referrals_with_tasks_to_auto_complete

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def mark_tasks_as_completed(
    refer_id: int, schema_name: str, db: AsyncSession
) -> Dict[str, Any]:
    """
    Mark all tasks for a referral as complete.

    Args:
        refer_id: Referral ID.
        schema_name: Schema name.
        db: SQLAlchemy async database session.

    Returns:
        Dict[str, Any]: Result of the operation.
    """
    try:
        ReferralTaskModel = await get_model(ReferralTask, schema_name)

        current_time = datetime.now(timezone.utc)

        stmt = (
            update(ReferralTaskModel)
            .where(
                and_(
                    ReferralTaskModel.refer_id == refer_id,
                    ReferralTaskModel.status != "Complete",
                    ReferralTaskModel.subject == TASK_SUBJECT,
                )
            )
            .values(
                status="Complete",
                modified_date=current_time,
                modified_task="Task auto-completed as all required fields were filled.",
            )
            .execution_options(synchronize_session=False)
        )

        await db.execute(stmt)
        await db.commit()

        return {
            "status": "SUCCESS",
            "message": f"Tasks marked as complete for refer_id: {refer_id}",
            "refer_id": refer_id,
        }

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )
