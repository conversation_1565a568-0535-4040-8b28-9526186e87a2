from typing import Type, Dict, Any
from db import Base
from sqlalchemy.sql.schema import Table, Column

_DYNAMIC_MODEL_CACHE: Dict[str, Any] = {}


async def get_model(base_model: Type[Base], schema_name: str) -> Any:
    cache_key = f"{base_model.__name__}_{schema_name}"

    if cache_key in _DYNAMIC_MODEL_CACHE:
        return _DYNAMIC_MODEL_CACHE[cache_key]

    new_columns = [
        Column(
            col.name,
            col.type,
            *col.foreign_keys,
            primary_key=col.primary_key,
            nullable=col.nullable,
            default=col.default,
            server_default=col.server_default,
            autoincrement=col.autoincrement,
            unique=col.unique,
            index=col.index,
        )
        for col in base_model.__table__.columns
    ]
    new_table = Table(
        base_model.__table__.name,
        Base.metadata,
        *new_columns,
        schema=schema_name,
        extend_existing=True,
    )
    DynamicModel = type(
        cache_key,
        (Base,),
        {"__tablename__": base_model.__tablename__, "__table__": new_table},
    )
    _DYNAMIC_MODEL_CACHE[cache_key] = DynamicModel
    return DynamicModel
